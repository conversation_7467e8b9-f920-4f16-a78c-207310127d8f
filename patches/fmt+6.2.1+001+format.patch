diff --git a/ios/Pods/fmt/src/format.cc b/ios/Pods/fmt/src/format.cc
index 1234567..abcdefg 100644
--- a/ios/Pods/fmt/src/format.cc
+++ b/ios/Pods/fmt/src/format.cc
@@ -16,25 +16,6 @@
 namespace fmt { namespace internal {
 
 template <>
-struct char_traits<fmt::internal::char8_type> {
-  using char_type = fmt::internal::char8_type;
-  using int_type = int;
-  using off_type = streamoff;
-  using pos_type = streampos;
-  using state_type = mbstate_t;
-
-  static void assign(char_type& c1, const char_type& c2) { c1 = c2; }
-  static bool eq(const char_type& c1, const char_type& c2) { return c1 == c2; }
-  static bool lt(const char_type& c1, const char_type& c2) { return c1 < c2; }
-
-  static int compare(const char_type* s1, const char_type* s2, size_t n) {
-    return memcmp(s1, s2, n);
-  }
-  static size_t length(const char_type* s) {
-    return strlen(reinterpret_cast<const char*>(s));
-  }
-  static const char_type* find(const char_type* s, size_t n, const char_type& a) {
-    return reinterpret_cast<const char_type*>(memchr(s, a, n));
-  }
-  static char_type* move(char_type* s1, const char_type* s2, size_t n) {
-    return reinterpret_cast<char_type*>(memmove(s1, s2, n));
-  }
-  static char_type* copy(char_type* s1, const char_type* s2, size_t n) {
-    return reinterpret_cast<char_type*>(memcpy(s1, s2, n));
-  }
-  static char_type* assign(char_type* s, size_t n, char_type a) {
-    return reinterpret_cast<char_type*>(memset(s, a, n));
-  }
-
-  static int_type not_eof(const int_type& c) { return eq_int_type(c, eof()) ? ~eof() : c; }
-  static char_type to_char_type(const int_type& c) { return static_cast<char_type>(c); }
-  static int_type to_int_type(const char_type& c) { return static_cast<int_type>(c); }
-  static bool eq_int_type(const int_type& c1, const int_type& c2) { return c1 == c2; }
-  static int_type eof() { return static_cast<int_type>(EOF); }
-};
-
-template <>
 FMT_FUNC int count_digits<4>(detail::fallback_uintptr n) {
   // fallback_uintptr is always stored in little endian.
   int i = static_cast<int>(sizeof(void*)) - 1;
