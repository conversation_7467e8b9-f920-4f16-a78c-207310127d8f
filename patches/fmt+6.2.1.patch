diff --git a/node_modules/fmt/include/fmt/core.h b/node_modules/fmt/include/fmt/core.h
index 1234567..abcdefg 100644
--- a/node_modules/fmt/include/fmt/core.h
+++ b/node_modules/fmt/include/fmt/core.h
@@ -330,11 +330,7 @@
 }

 #ifdef __cpp_char8_t
-using char8_type = char8_t;
-#else
-enum char8_type : unsigned char {};
-#endif
-}  // namespace internal
+// Disable char8_t support for Xcode 16.3+ compatibility
+#undef __cpp_char8_t
+#endif

 template <typename... Ts>
 using void_t = typename internal::void_t_impl<Ts...>::type;
@@ -440,10 +436,6 @@
 using string_view = basic_string_view<char>;
 using wstring_view = basic_string_view<wchar_t>;

-#ifndef __cpp_char8_t
-// char8_t is deprecated; use char instead.
-using char8_t FMT_DEPRECATED_ALIAS = internal::char8_type;
-#endif
-
 /** Specifies if ``T`` is a character type. Can be specialized by users. */
 template <typename T> struct is_char : std::false_type {};
 template <> struct is_char<char> : std::true_type {};
 template <> struct is_char<wchar_t> : std::true_type {};
-template <> struct is_char<internal::char8_type> : std::true_type {};
 template <> struct is_char<char16_t> : std::true_type {};
 template <> struct is_char<char32_t> : std::true_type {};

+}  // namespace internal
