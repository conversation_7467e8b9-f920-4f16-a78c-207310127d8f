diff --git a/ios/Pods/Headers/Private/fmt/fmt/core.h b/ios/Pods/Headers/Private/fmt/fmt/core.h
index 1234567..abcdefg 100644
--- a/ios/Pods/Headers/Private/fmt/fmt/core.h
+++ b/ios/Pods/Headers/Private/fmt/fmt/core.h
@@ -11,6 +11,7 @@
 #include <cstdio>  // std::FILE
 #include <cstring>
 #include <functional>
+#include <ios>
 #include <iterator>
 #include <memory>
 #include <string>
@@ -290,7 +291,7 @@
 #  define FMT_ASSERT(condition, message)                                       \
     ((condition) /* void */                                                    \
          ? void(0)                                                             \
-         : ::fmt::internal::assert_fail(__FILE__, __LINE__, (message)))
+         : ::fmt::v6::internal::assert_fail(__FILE__, __LINE__, (message)))
 #endif

 #ifndef FMT_USE_INT128
@@ -339,6 +340,10 @@
 }  // namespace internal

+// Disable char8_t support to avoid template specialization issues with Xcode
+#ifdef __cpp_char8_t
+#undef __cpp_char8_t
+#endif
+
 template <typename... Ts>
 using void_t = typename internal::void_t_impl<Ts...>::type;
