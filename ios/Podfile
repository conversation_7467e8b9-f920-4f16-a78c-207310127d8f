require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '14.0'

target 'ReactNativeBridgeIos' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false,
    :flipper_configuration => FlipperConfiguration.disabled
  )

  # pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'

  target 'ReactNativeBridgeIosTests' do
    inherit! :complete
    # Pods for testing
  end

  # Flipper is disabled via flipper_configuration above

  post_install do |installer|
  react_native_post_install(installer)

  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings["DEVELOPMENT_TEAM"] = "WF47643S5X"

        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] += [
          '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION'
        ]

        # 💥 Apply this fix ONLY to fmt
        if target.name == 'fmt'
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'

          # 🧨 This disables char8_t support completely
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] += [
            'FMT_USE_CHAR8_T=0',
            'FMT_DISABLE_CHAR8_T=1'
          ]

          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += [
            '-DFMT_USE_CHAR8_T=0',
            '-DFMT_DISABLE_CHAR8_T=1',
            '-Wno-builtin-macro-redefined'
          ]
        end

        # Optional: Silence char8_t warning for other RN targets
        if ['Yoga', 'React-Core', 'React-cxxreact', 'React-jsi', 'React-jsiexecutor', 'ReactCommon'].include?(target.name)
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] += ['-Wno-builtin-macro-redefined']
        end
      end
    end
    end
  end


end
